<ng-container *ngTemplateOutlet="(catalogService.isPaginatedView || !!catalogService.isRootView || catalogService.isShowFavorites) && card.type == 'CATEGORY' ? category : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="card.type == 'PRODUCT' ? product : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="catalogService.intoSubcategory && catalogService.isPaginatedView && card.type == 'CATEGORY' ? arrow : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="card.type == 'FAVORITES' ? favorites : null; context: { $implicit: card }"></ng-container>

<ng-template #category let-card>
  <div class="card category medium" longPress (mouseLongPress)="showPreviewSubcategories()" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)" [ngClass]="{'white-card': catalogService.intoSubcategory}">
    <div>
         <div class="icon">
             <img [src]="resolvedImageUrl | safe" width="100%" height="100%" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
             <!-- Label conteggio sottocategorie per root categories -->
             <div class="subcategories-count"
               *ngIf="catalogService.isRootView && card.subcategoriesCount !== null && card.subcategoriesCount !== undefined">
               {{card.subcategoriesCount}}
             </div>
         </div>
         <div class="detail">
             <div class="name" [innerHTML]="card.name"></div>
             <div class="open-category"><ion-icon name="caret-down-outline"></ion-icon></div>
         </div>
     </div>
 </div>
</ng-template>

<ng-template #product let-card>
  <div class="card product medium" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)" [ngClass]="{'white-card': catalogService.intoSubcategory}">
    <div [ngClass]="{'focusProd': card.focus === 'S'}">
        <div *ngIf="card.divisionStatusCode === 'Z3'" class="z3"></div>
         <div class="icon">
             <img [src]="resolvedImageUrl | safe" width="100%" height="100%" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
         </div>
         <div class="detail">
            <div class="name" [innerHTML]="card.name"></div>
            <div class="description" [innerHTML]="card.description"></div>
            <!--div class="to-be-continued">
              [...]
            </div-->
            <table>
              <tr>
                <td><ion-icon name="logo-euro" (click)="cardShowPrices($event)"></ion-icon></td>
                <td><ion-icon name="share-social-outline" (click)="share($event)"></ion-icon></td>
                <td>
                  <ion-icon *ngIf="!catalogService.isProspect && card.isFavorite" name="star" (click)="cardRemoveFavorite($event)"></ion-icon>
                  <ion-icon *ngIf="!catalogService.isProspect && !card.isFavorite" name="star-outline" (click)="cardAddFavorite($event)"></ion-icon>
                </td>
              </tr>
           </table>
         </div>
     </div>
 </div>
</ng-template>

<ng-template #arrow let-card>
  <div class="card category red-arrow">
    <div longPress (mouseLongPress)="showPreviewSubcategories()" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)" [ngClass]="{'white-card': catalogService.intoSubcategory}">
      <div class="name" [innerHTML]="card.name"></div>
    </div>
  </div>
</ng-template>

<ng-template #favorites let-card>
  <div class="card white-card favorites">
    <div>
         <div class="icon">
            <ion-icon name="star"></ion-icon>
         </div>
         <div class="detail">
             <div class="favorites-description">{{'CATALOG.FAVORITES_DESCRIPTION' | translate}}</div>
             <div class="name">{{card.name}}</div>
         </div>
     </div>
 </div>
</ng-template>

import { Injectable, inject } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
// Store imports removed as not used in this service
import { HybridDbService } from '../../shared/hybrid-db.service';
import { Customer } from '../../service/data/customer';
import { Category } from '../../service/data/category';
import {
  CustomerTagFilter,
  FilterConfiguration,
  CategoryFilterResult,
  TagFilterState,
  DatacolCategoryTag,
  Tag,
  TypologyContent
} from '../model/sector-hierarchy-response';
import { CatalogsService } from './catalog.service';

@Injectable({
  providedIn: 'root'
})
export class TagFilterService {
  private _dbService = inject(HybridDbService);
  private _catalogsService = inject(CatalogsService);

  // State management
  private _filterState = new BehaviorSubject<TagFilterState>({
    activeCustomerUid: null,
    currentFilter: null,
    availableConfigurations: [],
    isFilteringEnabled: false,
    lastFilterUpdate: 0
  });

  public filterState$ = this._filterState.asObservable();

  // Subject per notificare i cambiamenti dei filtri
  private _filtersChanged = new BehaviorSubject<boolean>(false);

  // Subject per notificare che i conteggi devono essere ricalcolati
  private _countsNeedUpdate = new BehaviorSubject<boolean>(false);
  public filtersChanged$ = this._filtersChanged.asObservable();
  public countsNeedUpdate$ = this._countsNeedUpdate.asObservable();

  // Cache per le performance
  private _categoryTagsCache = new Map<string, string[]>();
  private _cacheTimestamp = 0;
  private _cacheValidityMs = 5 * 60 * 1000; // 5 minuti

  constructor() {
    this.initializeService();
  }

  /**
   * Inizializza il servizio e carica le configurazioni salvate
   */
  private async initializeService(): Promise<void> {
    try {
      // Carica le configurazioni salvate dal database
      await this.loadSavedConfigurations();

      // Carica automaticamente l'ultimo stato del filtro se disponibile
      await this.loadLastUsedFilterState();

      console.log('🔄 [TAG_FILTER] TagFilterService inizializzato - filtri persistenti caricati');
    } catch (error) {
      console.log('🔄 [TAG_FILTER] TagFilterService inizializzato senza filtri persistenti');
    }
  }

  /**
   * Carica l'ultimo stato del filtro utilizzato
   */
  private async loadLastUsedFilterState(): Promise<void> {
    try {
      // Prova a recuperare l'ultimo customer UID utilizzato dal localStorage
      const lastCustomerUid = localStorage.getItem('lastActiveCustomerUid');

      if (lastCustomerUid) {
        const savedFilter = await this.loadCustomerFilterState(lastCustomerUid);
        if (savedFilter) {
          // Imposta direttamente lo stato del filtro
          const currentState = this._filterState.value;
          this._filterState.next({
            ...currentState,
            activeCustomerUid: lastCustomerUid,
            currentFilter: savedFilter,
            isFilteringEnabled: true,
            lastFilterUpdate: Date.now()
          });

          // Salva il customer UID nel localStorage per la prossima volta
          localStorage.setItem('lastActiveCustomerUid', lastCustomerUid);

          console.log('🔄 [TAG_FILTER] Filtri persistenti caricati per customer:', lastCustomerUid);
        }
      }
    } catch (error) {
      // Ignora errori nel caricamento dello stato persistente
    }
  }

  /**
   * Imposta il cliente attivo e carica i suoi filtri
   */
  async setActiveCustomer(customerUid: string): Promise<void> {
    try {
      console.log(`🔄 Impostazione cliente attivo: ${customerUid}`);
      
      // Carica i dati del cliente
      const customer = await this.getCustomerData(customerUid);
      if (!customer) {
        throw new Error(`Cliente ${customerUid} non trovato`);
      }

      // Crea il filtro predefinito basato sui dati del cliente
      const defaultFilter = await this.createDefaultFilter(customer);
      
      // Carica le configurazioni salvate per questo cliente
      const savedConfigurations = await this.loadCustomerConfigurations(customerUid);
      
      // Aggiorna lo stato
      const newState: TagFilterState = {
        activeCustomerUid: customerUid,
        currentFilter: defaultFilter,
        availableConfigurations: savedConfigurations,
        isFilteringEnabled: true,
        lastFilterUpdate: Date.now()
      };

      this._filterState.next(newState);

      // Salva il customer UID nel localStorage per persistenza
      localStorage.setItem('lastActiveCustomerUid', customerUid);

      console.log('✅ Cliente attivo impostato con successo');
      
    } catch (error) {
      console.error('❌ Errore nell\'impostazione del cliente attivo:', error);
      throw error;
    }
  }

  /**
   * Crea il filtro predefinito basato sui dati del cliente
   */
  private async createDefaultFilter(customer: Customer): Promise<CustomerTagFilter> {
    console.log(`🔧 [TAG_FILTER] Creazione filtro predefinito per cliente ${customer.uid}`);

    // Estrae i dati del settore dal cliente
    const division = customer.infoDataList?.find(info => info.division)?.division || '';
    const codiceAttivita = customer.codiceAttivita || '';
    const codiceProfessione = customer.codiceProfessione || '';

    console.log(`📊 [TAG_FILTER] Dati cliente - Division: ${division}, Attività: ${codiceAttivita}, Professione: ${codiceProfessione}`);

    // Determina i tag basandosi sui codici del cliente
    const settoreTag = this.mapDivisionToSectorTag(division);
    const professioneTag = this.mapCodiceProfessioneToTag(codiceProfessione);

    const filter: CustomerTagFilter = {
      customerUid: customer.uid,
      settore: settoreTag,
      attivita: codiceAttivita, // Per ora usa direttamente il codice attività
      professione: professioneTag,
      universale: true, // Il tag universale è sempre incluso
      customTags: [],
      isDefault: true
    };

    console.log('✅ [TAG_FILTER] Filtro predefinito creato:', filter);
    return filter;
  }

  /**
   * Ottiene i dati del cliente dal database
   */
  private async getCustomerData(customerUid: string): Promise<Customer | null> {
    try {
      const customers = await this._dbService.getRecordsByANDCondition(
        'customers',
        [{ key: 'uid', value: customerUid }]
      ) as Customer[];

      console.log(`📊 [TAG_FILTER] Dati cliente recuperati: ${customers.length} record`);
      console.log(customers);
      
      return customers.length > 0 ? customers[0] : null;
    } catch (error) {
      console.error('❌ Errore nel recupero dati cliente:', error);
      return null;
    }
  }

  /**
   * Carica le configurazioni salvate per un cliente
   */
  private async loadCustomerConfigurations(customerUid: string): Promise<FilterConfiguration[]> {
    try {
      const records = await this._dbService.getRecordsByANDCondition(
        'filter_configurations',
        [{ key: 'customerUid', value: customerUid }]
      );

      return records.map(record => ({
        id: record.id,
        customerUid: record.customerUid,
        name: record.name,
        activeTags: JSON.parse(record.activeTags || '[]'),
        isDefault: record.isDefault === 1,
        createdAt: record.createdAt,
        lastUsed: record.lastUsed
      }));
    } catch (error) {
      console.error('❌ Errore nel caricamento configurazioni cliente:', error);
      return [];
    }
  }

  /**
   * Carica tutte le configurazioni salvate
   */
  private async loadSavedConfigurations(): Promise<void> {
    try {
      const records = await this._dbService.getAll(['filter_configurations']);
      console.log(`📂 Caricate ${records.length} configurazioni dal database`); 
    } catch (error) {
      console.error('❌ Errore nel caricamento configurazioni:', error);
    }
  }

  /**
   * Salva una configurazione di filtri nel database
   */
  async saveFilterConfiguration(configuration: FilterConfiguration): Promise<void> {
    try {
      const record = {
        id: configuration.id,
        customerUid: configuration.customerUid,
        name: configuration.name,
        activeTags: JSON.stringify(configuration.activeTags),
        isDefault: configuration.isDefault ? 1 : 0,
        createdAt: configuration.createdAt,
        lastUsed: configuration.lastUsed
      };

      await this._dbService.addRecord(
        'filter_configurations',
        Object.keys(record),
        Object.values(record).map(v => String(v))
      );

    } catch (error) {
      throw error;
    }
  }

  /**
   * Aggiorna una configurazione esistente
   */
  async updateFilterConfiguration(configuration: FilterConfiguration): Promise<void> {
    try {
      const record = {
        customerUid: configuration.customerUid,
        name: configuration.name,
        activeTags: JSON.stringify(configuration.activeTags),
        isDefault: configuration.isDefault ? 1 : 0,
        createdAt: configuration.createdAt,
        lastUsed: Date.now() // Aggiorna il timestamp di ultimo utilizzo
      };

      const updateColumns = Object.keys(record).map(key => ({
        key: key,
        value: String(record[key])
      }));

      await this._dbService.updateRecord(
        'filter_configurations',
        [{ key: 'id', value: configuration.id }],
        updateColumns
      );

      console.log(`✅ Configurazione ${configuration.name} aggiornata`);
    } catch (error) {
      console.error('❌ Errore nell\'aggiornamento configurazione:', error);
      throw error;
    }
  }

  /**
   * Elimina una configurazione
   */
  async deleteFilterConfiguration(configurationId: string): Promise<void> {
    try {
      await this._dbService.deleteRecord(
        'filter_configurations',
        [{ key: 'id', value: configurationId }]
      );

      console.log(`✅ Configurazione ${configurationId} eliminata`);
    } catch (error) {
      console.error('❌ Errore nell\'eliminazione configurazione:', error);
      throw error;
    }
  }

  /**
   * Salva lo stato del filtro per un cliente
   */
  async saveCustomerFilterState(customerUid: string, filter: CustomerTagFilter): Promise<void> {
    try {
      // Verifica che le tabelle esistano prima di procedere
      await this._dbService.ensureTagFilterTablesExist();

      const record = {
        customerUid: customerUid,
        currentFilterId: null, // Per ora non gestiamo ID specifici
        isFilteringEnabled: filter ? 1 : 0,
        lastFilterUpdate: Date.now(),
        settore: filter?.settore || null,
        attivita: filter?.attivita || null,
        professione: filter?.professione || null,
        customTags: JSON.stringify(filter?.customTags || [])
      };

      // Verifica se esiste già un record per questo cliente
      const existingRecords = await this._dbService.getRecordsByANDCondition(
        'customer_filter_state',
        [{ key: 'customerUid', value: customerUid }]
      );

      if (existingRecords.length > 0) {
        // Aggiorna il record esistente
        const updateColumns = Object.keys(record).map(key => ({
          key: key,
          value: String(record[key])
        }));

        await this._dbService.updateRecord(
          'customer_filter_state',
          [{ key: 'customerUid', value: customerUid }],
          updateColumns
        );
      } else {
        // Crea un nuovo record
        await this._dbService.addRecord(
          'customer_filter_state',
          Object.keys(record),
          Object.values(record)
        );
      }

      console.log(`✅ Stato filtro salvato per cliente ${customerUid}`);
    } catch (error) {
      console.error('❌ Errore nel salvataggio stato filtro:', error);
      throw error;
    }
  }

  /**
   * Carica lo stato del filtro per un cliente
   */
  async loadCustomerFilterState(customerUid: string): Promise<CustomerTagFilter | null> {
    try {
      const records = await this._dbService.getRecordsByANDCondition(
        'customer_filter_state',
        [{ key: 'customerUid', value: customerUid }]
      );

      if (records.length === 0) {
        return null;
      }

      const record = records[0];
      return {
        customerUid: record.customerUid,
        settore: record.settore,
        attivita: record.attivita,
        professione: record.professione,
        universale: true, // Sempre abilitato
        customTags: JSON.parse(record.customTags || '[]'),
        isDefault: !record.settore && !record.attivita && !record.professione &&
                   JSON.parse(record.customTags || '[]').length === 0
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Applica i filtri alle categorie e restituisce il risultato
   * DEPRECATO: Utilizzare getFilteredCategories() per performance migliori
   */
  async applyFiltersToCategories(categories: Category[]): Promise<CategoryFilterResult> {
    const currentState = this._filterState.value;

    if (!currentState.isFilteringEnabled || !currentState.currentFilter) {
      console.log('🏷️ [TAG_FILTER] Filtraggio disabilitato - mostro tutte le categorie');
      // Se il filtraggio non è abilitato, mostra tutte le categorie
      return {
        visibleCategories: categories.map(c => c.id),
        hiddenCategories: [],
        totalVisible: categories.length,
        totalHidden: 0
      };
    }

    // Log dei filtri attivi
    const filter = currentState.currentFilter;
    const activeTags = this.getActiveTagsFromFilter(filter);
    console.log('🏷️ [TAG_FILTER] Filtri tag applicati (metodo legacy):', {
      settore: filter.settore || 'Non impostato',
      attivita: filter.attivita || 'Non impostato',
      professione: filter.professione || 'Non impostato',
      universale: filter.universale ? 'Attivo' : 'Non attivo',
      customTags: filter.customTags || [],
      totalActiveTags: activeTags.length
    });

    try {
      const visibleCategories: string[] = [];
      const hiddenCategories: string[] = [];

      // Filtra le DatacolCategory (quelle con isProduct = true)
      for (const category of categories) {
        if (category.isProduct) {
          const isVisible = await this.isCategoryVisible(category, currentState.currentFilter);
          if (isVisible) {
            visibleCategories.push(category.id);
          } else {
            hiddenCategories.push(category.id);
          }
        } else {
          // Per le categorie non-prodotto, verifica se hanno figli visibili
          const hasVisibleChildren = await this.hasVisibleChildren(category, categories, currentState.currentFilter);
          if (hasVisibleChildren) {
            visibleCategories.push(category.id);
          } else {
            hiddenCategories.push(category.id);
          }
        }
      }

      return {
        visibleCategories,
        hiddenCategories,
        totalVisible: visibleCategories.length,
        totalHidden: hiddenCategories.length
      };

    } catch (error) {
      console.error('❌ Errore nell\'applicazione dei filtri:', error);
      // In caso di errore, mostra tutte le categorie
      return {
        visibleCategories: categories.map(c => c.id),
        hiddenCategories: [],
        totalVisible: categories.length,
        totalHidden: 0
      };
    }
  }

  /**
   * Ottiene le categorie già filtrate direttamente dal database (OTTIMIZZATO)
   * Questo metodo è molto più veloce del precedente applyFiltersToCategories
   * @param idCatalog ID del catalogo (default: 1)
   * @param columnList Colonne da selezionare (opzionale)
   * @returns Promise<Category[]> Categorie filtrate
   */
  async getFilteredCategories(idCatalog: number = 1, columnList?: string[]): Promise<Category[]> {
    const currentState = this._filterState.value;

    if (!currentState.isFilteringEnabled || !currentState.currentFilter) {
      console.log('🏷️ [TAG_FILTER] Filtraggio disabilitato - carico tutte le categorie');
      return this._dbService.getAll(['categories'], columnList);
    }

    // Crea l'array dei tag attivi per il filtro
    const activeTags = this.getActiveTagsFromFilter(currentState.currentFilter);

    if (activeTags.length === 0) {
      console.log('🏷️ [TAG_FILTER] Nessun tag attivo - carico tutte le categorie');
      return this._dbService.getAll(['categories'], columnList);
    }

    try {
      const startTime = Date.now();

      // Utilizza il nuovo metodo ottimizzato del database
      const filteredCategories = await this._dbService.getFilteredCategories(activeTags, idCatalog, columnList);

      const endTime = Date.now();
      console.log(`✅ [TAG_FILTER] Categorie filtrate in ${endTime - startTime}ms: ${filteredCategories.length} risultati`);

      return filteredCategories;

    } catch (error) {
      console.error('❌ Errore nel caricamento categorie filtrate:', error);
      // Fallback: carica tutte le categorie
      return this._dbService.getAll(['categories'], columnList);
    }
  }

  /**
   * Ottiene i prodotti già filtrati direttamente dal database (NUOVO - OTTIMIZZATO)
   * Questo metodo è molto più performante del caricamento di tutti i prodotti in memoria
   * @param idCatalog ID del catalogo (default: 1)
   * @param columnList Colonne da selezionare (opzionale)
   * @param additionalConditions Condizioni aggiuntive per il filtro (es. idSubCategory)
   * @returns Promise<any[]> Prodotti filtrati
   */
  async getFilteredProducts(
    idCatalog: number = 1,
    columnList?: string[],
    additionalConditions?: {key: string, value: string}[]
  ): Promise<any[]> {
    const currentState = this._filterState.value;

    if (!currentState.isFilteringEnabled || !currentState.currentFilter) {
      console.log('🏷️ [TAG_FILTER] Filtraggio disabilitato - carico tutti i prodotti');
      if (additionalConditions && additionalConditions.length > 0) {
        return this._dbService.getRecordsByANDCondition('products', additionalConditions);
      }
      return this._dbService.getAll(['products'], columnList);
    }

    // Crea l'array dei tag attivi per il filtro
    const activeTags = this.getActiveTagsFromFilter(currentState.currentFilter);

    if (activeTags.length === 0) {
      console.log('🏷️ [TAG_FILTER] Nessun tag attivo - carico tutti i prodotti');
      if (additionalConditions && additionalConditions.length > 0) {
        return this._dbService.getRecordsByANDCondition('products', additionalConditions);
      }
      return this._dbService.getAll(['products'], columnList);
    }

    try {
      const startTime = Date.now();

      // Utilizza il nuovo metodo ottimizzato del database per i prodotti
      const filteredProducts = await this._dbService.getFilteredProducts(
        activeTags,
        idCatalog,
        columnList,
        additionalConditions
      );

      const endTime = Date.now();
      console.log(`✅ [TAG_FILTER] Prodotti filtrati in ${endTime - startTime}ms: ${filteredProducts.length} risultati`);

      return filteredProducts;

    } catch (error) {
      console.error('❌ Errore nel caricamento prodotti filtrati:', error);
      // Fallback: usa le condizioni aggiuntive o carica tutti i prodotti
      if (additionalConditions && additionalConditions.length > 0) {
        return this._dbService.getRecordsByANDCondition('products', additionalConditions);
      }
      return this._dbService.getAll(['products'], columnList);
    }
  }

  /**
   * Calcola il numero di sottocategorie visibili per una root category con i filtri applicati
   * @param rootCategory Root category per cui calcolare il conteggio
   * @param idCatalog ID del catalogo (default: 1)
   * @returns Promise<number> Numero di sottocategorie visibili
   */
  async calculateFilteredSubcategoriesCount(rootCategory: any, idCatalog: number = 1): Promise<number> {
    try {
      const currentState = this._filterState.value;

      // Se il filtraggio è disabilitato, usa il conteggio normale
      if (!currentState.isFilteringEnabled || !currentState.currentFilter) {
        return this.calculateNormalSubcategoriesCount(rootCategory);
      }

      // Ottieni le categorie filtrate
      const filteredCategories = await this.getFilteredCategories(idCatalog);

      // Conta le sottocategorie che appartengono a questa root category
      const subcategoriesCount = filteredCategories.filter(cat => {
        console.log(cat.isProduct)
        return cat.idRootCategory === rootCategory.id &&
               (cat.isProduct === true) && // Solo le categorie prodotto
               cat.id !== rootCategory.id; // Escludi la root category stessa
      }).length;

      console.log(`🔢 [FILTERED_COUNT] Root category "${rootCategory.name}" (ID: ${rootCategory.id}) ha ${subcategoriesCount} sottocategorie visibili con filtri`);
      return subcategoriesCount;

    } catch (error) {
      console.error(`❌ Errore nel calcolo sottocategorie filtrate per root category ${rootCategory.id}:`, error);
      // Fallback al conteggio normale
      return this.calculateNormalSubcategoriesCount(rootCategory);
    }
  }

  /**
   * Calcola il conteggio normale delle sottocategorie (senza filtri)
   * @param rootCategory Root category per cui calcolare il conteggio
   * @returns Promise<number> Numero di sottocategorie totali
   */
  private async calculateNormalSubcategoriesCount(rootCategory: any): Promise<number> {
    try {
      if (!rootCategory.idApp) {
        return 0;
      }

      // Ottieni tutte le categorie
      const allCategories = await this._dbService.getAll(['categories']);

      // Conta le categorie che hanno come parent questa root category
      const subcategoriesCount = allCategories.filter(cat => {
        return cat.idApp &&
               cat.idApp !== rootCategory.idApp &&
               cat.idApp.includes(rootCategory.idApp) &&
               (cat.isProduct === 'true' || cat.isProduct === true); // Solo le categorie prodotto
      }).length;

      return subcategoriesCount;

    } catch (error) {
      console.error(`❌ Errore nel calcolo sottocategorie normali per root category ${rootCategory.id}:`, error);
      return 0;
    }
  }

  /**
   * Calcola i conteggi filtrati per tutte le root categories
   * @param rootCategories Array di root categories
   * @param idCatalog ID del catalogo (default: 1)
   * @returns Promise<any[]> Root categories con conteggi aggiornati
   */
  async calculateFilteredSubcategoriesCountForRootCategories(rootCategories: any[], idCatalog: number = 1): Promise<any[]> {
    try {
      console.log(`📊 [FILTERED_COUNT] Calcolo conteggi filtrati per ${rootCategories.length} root categories`);

      const updatedRootCategories = await Promise.all(
        rootCategories.map(async (rootCategory) => {
          const subcategoriesCount = await this.calculateFilteredSubcategoriesCount(rootCategory, idCatalog);
          console.log(`📊 [FILTERED_COUNT] Root "${rootCategory.name}" (ID: ${rootCategory.id}) contiene ${subcategoriesCount} sottocategorie visibili`);

          return {
            ...rootCategory,
            subcategoriesCount: subcategoriesCount
          };
        })
      );

      return updatedRootCategories;

    } catch (error) {
      console.error('❌ Errore nel calcolo conteggi filtrati per root categories:', error);
      // Fallback: restituisce le root categories senza modifiche
      return rootCategories;
    }
  }

  /**
   * Ottiene le root categories che hanno figli visibili (OTTIMIZZATO)
   * @param idCatalog ID del catalogo (default: 1)
   * @returns Promise<Category[]> Root categories filtrate
   */
  async getFilteredRootCategories(idCatalog: number = 1): Promise<Category[]> {
    const currentState = this._filterState.value;

    if (!currentState.isFilteringEnabled || !currentState.currentFilter) {
      console.log('🏷️ [TAG_FILTER] Filtraggio disabilitato - carico tutte le root categories');
      const allCategories = await this._dbService.getAll(['categories']);
      return allCategories.filter(cat =>
        cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
      );
    }

    const filter = currentState.currentFilter;
    const activeTags = this.getActiveTagsFromFilter(filter);

    console.log('🚀 [TAG_FILTER] Caricamento root categories ottimizzato:', {
      totalActiveTags: activeTags.length,
      idCatalog
    });

    try {
      const startTime = Date.now();

      // Utilizza il nuovo metodo ottimizzato del database
      const filteredRootCategories = await this._dbService.getFilteredRootCategories(activeTags, idCatalog);

      const endTime = Date.now();
      console.log(`✅ [TAG_FILTER] Root categories filtrate in ${endTime - startTime}ms: ${filteredRootCategories.length} risultati`);

      return filteredRootCategories;

    } catch (error) {
      console.error('❌ Errore nel caricamento root categories filtrate:', error);
      // Fallback: carica tutte le root categories
      const allCategories = await this._dbService.getAll(['categories']);
      return allCategories.filter(cat =>
        cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
      );
    }
  }

  /**
   * Verifica se una categoria è visibile in base ai filtri
   */
  private async isCategoryVisible(category: Category, filter: CustomerTagFilter): Promise<boolean> {
    try {
      // Validazione dati categoria
      if (!category || !category.id) {
        console.warn('⚠️ [TAG_FILTER] Categoria non valida per verifica visibilità:', category);
        return false;
      }

      // Validazione filtro
      if (!filter) {
        console.warn('⚠️ [TAG_FILTER] Filtro non valido per categoria:', category.name);
        return false;
      }

      // Ottieni i tag assegnati alla categoria
      const categoryTags = await this.getCategoryTags(category);

      if (categoryTags.length === 0) {
        console.log(`🏷️ [TAG_FILTER] Categoria ${category.name} senza tag - non visibile`);
        return false;
      }

      // Crea l'array dei tag attivi per il filtro
      const activeTags = this.getActiveTagsFromFilter(filter);

      if (activeTags.length === 0) {
        console.warn('⚠️ [TAG_FILTER] Nessun tag attivo nel filtro');
        return false;
      }

      // Verifica se almeno uno dei tag della categoria è presente nei tag attivi
      const isVisible = categoryTags.some(tag => {
        if (!tag || !tag.keyTag) {
          console.warn('⚠️ [TAG_FILTER] Tag categoria non valido:', tag);
          return false;
        }
        return activeTags.includes(tag.keyTag);
      });

      console.log(`🏷️ [TAG_FILTER] Categoria ${category.name}: ${isVisible ? 'VISIBILE' : 'NASCOSTA'} (${categoryTags.length} tag, ${activeTags.length} filtri attivi)`);

      return isVisible;

    } catch (error) {
      console.error('❌ Errore nella verifica visibilità categoria:', {
        categoryName: category?.name || 'Unknown',
        categoryId: category?.id || 'Unknown',
        error: error
      });
      return false;
    }
  }

  /**
   * Ottiene i tag assegnati a una categoria (completamente offline)
   */
  private async getCategoryTags(category: Category): Promise<DatacolCategoryTag[]> {
    try {
      // Validazione dati categoria
      if (!category) {
        console.warn('⚠️ [TAG_FILTER] Categoria null/undefined in getCategoryTags');
        return [];
      }

      if (!category.idSubCategory || !category.idCatalog) {
        console.log(`🏷️ [TAG_FILTER] Categoria ${category.name} senza idSubCategory o idCatalog - nessun tag`);
        return [];
      }

      // Validazione e parsing degli ID
      let idSubCategory: number;
      try {
        idSubCategory = parseInt(category.idSubCategory);
        if (isNaN(idSubCategory)) {
          console.warn(`⚠️ [TAG_FILTER] idSubCategory non valido per categoria ${category.name}: ${category.idSubCategory}`);
          return [];
        }
      } catch (parseError) {
        console.error(`❌ [TAG_FILTER] Errore nel parsing idSubCategory per categoria ${category.name}:`, parseError);
        return [];
      }

      const idCatalog = category.idCatalog;
      if (typeof idCatalog !== 'number' || idCatalog <= 0) {
        console.warn(`⚠️ [TAG_FILTER] idCatalog non valido per categoria ${category.name}: ${idCatalog}`);
        return [];
      }

      // Recupera sempre dal database locale (funzionamento offline)
      const cachedTags = await this.getCachedCategoryTags(idSubCategory, idCatalog);

      console.log(`🏷️ [OFFLINE_FILTER] Categoria ${category.name} (${idSubCategory}): ${cachedTags.length} tag trovati nel database locale`);

      return cachedTags;
    } catch (error) {
      console.error(`❌ [OFFLINE_FILTER] Errore nel recupero tag categoria ${category?.name || 'Unknown'}:`, {
        categoryId: category?.id || 'Unknown',
        idSubCategory: category?.idSubCategory || 'Unknown',
        idCatalog: category?.idCatalog || 'Unknown',
        error: error
      });
      return [];
    }
  }

  /**
   * Recupera i tag di una categoria dalla cache locale con caching in memoria
   */
  private async getCachedCategoryTags(idSubCategory: number, idCatalog: number): Promise<DatacolCategoryTag[]> {
    try {
      // Validazione parametri
      if (!idSubCategory || !idCatalog || idSubCategory <= 0 || idCatalog <= 0) {
        console.warn(`⚠️ [TAG_FILTER] Parametri non validi per getCachedCategoryTags: idSubCategory=${idSubCategory}, idCatalog=${idCatalog}`);
        return [];
      }

      const cacheKey = `${idSubCategory}_${idCatalog}`;
      const now = Date.now();

      // Verifica se la cache è valida
      if (this._cacheTimestamp > 0 && (now - this._cacheTimestamp) < this._cacheValidityMs) {
        const cachedTags = this._categoryTagsCache.get(cacheKey);
        if (cachedTags !== undefined) {
          console.log(`💾 [TAG_FILTER] Tag recuperati dalla cache in memoria per ${cacheKey}: ${cachedTags.length} tag`);
          return cachedTags.map(keyTag => ({
            keyTag: keyTag,
            description: ''
          }));
        }
      }

      // Recupera dal database
      console.log(`🔍 [TAG_FILTER] Recupero tag dal database per categoria ${idSubCategory}, catalogo ${idCatalog}`);

      const records = await this._dbService.getRecordsByANDCondition(
        'datacol_category_tags',
        [
          { key: 'idSubCategory', value: String(idSubCategory) },
          { key: 'idCatalog', value: String(idCatalog) }
        ]
      );

      if (!records || !Array.isArray(records)) {
        console.warn(`⚠️ [TAG_FILTER] Risultato database non valido per categoria ${idSubCategory}`);
        return [];
      }

      const tags = records.map(record => {
        if (!record || typeof record !== 'object') {
          console.warn(`⚠️ [TAG_FILTER] Record non valido:`, record);
          return null;
        }
        return {
          keyTag: record.keyTag || '',
          description: record.description || ''
        };
      }).filter(tag => tag !== null && tag.keyTag) as DatacolCategoryTag[];

      // Salva nella cache solo i keyTag per ottimizzare la memoria
      const keyTags = tags.map(tag => tag.keyTag).filter(tag => tag);
      this._categoryTagsCache.set(cacheKey, keyTags);
      if (this._cacheTimestamp === 0) {
        this._cacheTimestamp = now;
      }

      console.log(`✅ [TAG_FILTER] Recuperati ${tags.length} tag dal database per categoria ${idSubCategory}`);
      return tags;

    } catch (error) {
      console.error(`❌ Errore nel recupero tag dalla cache per categoria ${idSubCategory}:`, {
        idSubCategory,
        idCatalog,
        error: error
      });
      return [];
    }
  }

  /**
   * Salva i tag di una categoria nella cache locale
   */
  private async cacheCategoryTags(idSubCategory: number, idCatalog: number, tags: DatacolCategoryTag[]): Promise<void> {
    try {
      // Prima elimina i tag esistenti per questa categoria
      await this._dbService.deleteRecord(
        'datacol_category_tags',
        [
          { key: 'idSubCategory', value: String(idSubCategory) },
          { key: 'idCatalog', value: String(idCatalog) }
        ]
      );

      // Poi inserisce i nuovi tag
      const timestamp = Date.now();
      for (const tag of tags) {
        await this._dbService.addRecord(
          'datacol_category_tags',
          ['idSubCategory', 'idCatalog', 'keyTag', 'description', 'lastSync'],
          [String(idSubCategory), String(idCatalog), tag.keyTag, tag.description || '', String(timestamp)]
        );
      }

      console.log(`✅ Cached ${tags.length} tags for category ${idSubCategory}`);
    } catch (error) {
      console.error('❌ Errore nel salvataggio tag in cache:', error);
    }
  }

  /**
   * Pulisce la cache dei tag più vecchi di un certo timestamp
   */
  async cleanupTagCache(olderThanTimestamp: number): Promise<void> {
    try {
      // Nota: questo richiederebbe un metodo personalizzato per query complesse
      // Per ora logghiamo l'operazione
      console.log(`🧹 Cleanup cache tag più vecchi di ${olderThanTimestamp}`);

      // In futuro si potrebbe implementare una query personalizzata:
      // DELETE FROM datacol_category_tags WHERE lastSync < olderThanTimestamp
    } catch (error) {
      console.error('❌ Errore nel cleanup cache tag:', error);
    }
  }

  /**
   * Estrae i tag attivi dal filtro
   */
  private getActiveTagsFromFilter(filter: CustomerTagFilter): string[] {
    const activeTags: string[] = [];
    
    // Aggiungi i tag predefiniti del cliente
    if (filter.settore) activeTags.push(filter.settore);
    if (filter.attivita) activeTags.push(filter.attivita);
    if (filter.professione) activeTags.push(filter.professione);
    if (filter.universale) activeTags.push('UNIVERSALE'); // Assumendo che il tag universale abbia questa chiave
    
    // Aggiungi i tag personalizzati
    activeTags.push(...filter.customTags);
    
    return activeTags;
  }

  /**
   * Verifica se una categoria ha figli visibili
   */
  private async hasVisibleChildren(
    parentCategory: Category,
    allCategories: Category[],
    filter: CustomerTagFilter,
    visitedCategories: Set<string> = new Set()
  ): Promise<boolean> {
    try {
      // Prevenzione di cicli infiniti
      if (visitedCategories.has(parentCategory.id)) {
        console.warn(`🔄 [TAG_FILTER] Ciclo rilevato per categoria ${parentCategory.name} (${parentCategory.id})`);
        return false;
      }
      visitedCategories.add(parentCategory.id);

      // Validazione dati categoria padre
      if (!parentCategory || !parentCategory.id) {
        console.warn('⚠️ [TAG_FILTER] Categoria padre non valida:', parentCategory);
        return false;
      }

      // Trova le categorie figlie
      const children = allCategories.filter(cat => {
        if (!cat || !cat.id) return false;
        return cat.idParent === parentCategory.id ||
               (cat.idApp && parentCategory.idApp &&
                cat.idApp.startsWith(parentCategory.idApp) &&
                cat.id !== parentCategory.id);
      });

      console.log(`🔍 [TAG_FILTER] Categoria ${parentCategory.name}: trovati ${children.length} figli`);

      // Verifica ricorsivamente se almeno un figlio è visibile
      for (const child of children) {
        try {
          if (child.isProduct) {
            const isVisible = await this.isCategoryVisible(child, filter);
            if (isVisible) {
              console.log(`✅ [TAG_FILTER] Figlio visibile trovato: ${child.name}`);
              return true;
            }
          } else {
            const hasVisibleGrandchildren = await this.hasVisibleChildren(
              child,
              allCategories,
              filter,
              new Set(visitedCategories) // Crea una nuova copia del set per ogni ramo
            );
            if (hasVisibleGrandchildren) {
              console.log(`✅ [TAG_FILTER] Nipoti visibili trovati per: ${child.name}`);
              return true;
            }
          }
        } catch (childError) {
          console.error(`❌ [TAG_FILTER] Errore nella verifica figlio ${child.name}:`, childError);
          // Continua con il prossimo figlio invece di interrompere tutto
          continue;
        }
      }

      return false;
    } catch (error) {
      console.error('❌ Errore nella verifica figli visibili:', {
        parentCategory: parentCategory?.name || 'Unknown',
        parentId: parentCategory?.id || 'Unknown',
        error: error
      });
      return false;
    }
  }

  /**
   * Aggiunge un tag personalizzato al filtro corrente
   */
  async addCustomTag(keyTag: string): Promise<void> {
    const currentState = this._filterState.value;
    
    if (!currentState.currentFilter) {
      throw new Error('Nessun filtro attivo');
    }

    // Verifica che il tag non sia già presente
    if (currentState.currentFilter.customTags.includes(keyTag)) {
      console.warn(`Tag ${keyTag} già presente nei filtri personalizzati`);
      return;
    }

    // Aggiunge il tag ai filtri personalizzati
    const updatedFilter: CustomerTagFilter = {
      ...currentState.currentFilter,
      customTags: [...currentState.currentFilter.customTags, keyTag],
      isDefault: false // Non è più la configurazione predefinita
    };

    const newState: TagFilterState = {
      ...currentState,
      currentFilter: updatedFilter,
      lastFilterUpdate: Date.now()
    };

    this._filterState.next(newState);

    // Salva automaticamente lo stato aggiornato
    if (currentState.activeCustomerUid) {
      await this.saveCustomerFilterState(currentState.activeCustomerUid, updatedFilter);
    }

    // Notifica il cambiamento dei filtri
    this._filtersChanged.next(true);

    console.log(`✅ Tag personalizzato ${keyTag} aggiunto`);
  }

  /**
   * Rimuove un tag personalizzato dal filtro corrente
   */
  async removeCustomTag(keyTag: string): Promise<void> {
    const currentState = this._filterState.value;
    
    if (!currentState.currentFilter) {
      throw new Error('Nessun filtro attivo');
    }

    // Verifica che non sia un tag predefinito (non rimovibile)
    const predefinedTags = [
      currentState.currentFilter.settore,
      currentState.currentFilter.attivita,
      currentState.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    if (predefinedTags.includes(keyTag)) {
      throw new Error(`Il tag ${keyTag} è predefinito e non può essere rimosso`);
    }

    // Rimuove il tag dai filtri personalizzati
    const updatedFilter: CustomerTagFilter = {
      ...currentState.currentFilter,
      customTags: currentState.currentFilter.customTags.filter(tag => tag !== keyTag)
    };

    const newState: TagFilterState = {
      ...currentState,
      currentFilter: updatedFilter,
      lastFilterUpdate: Date.now()
    };

    this._filterState.next(newState);

    // Salva automaticamente lo stato aggiornato
    if (currentState.activeCustomerUid) {
      await this.saveCustomerFilterState(currentState.activeCustomerUid, updatedFilter);
    }

    // Notifica il cambiamento dei filtri
    this._filtersChanged.next(true);

    console.log(`✅ Tag personalizzato ${keyTag} rimosso`);
  }

  /**
   * Ottiene lo stato corrente del filtro
   */
  getCurrentFilter(): CustomerTagFilter | null {
    return this._filterState.value.currentFilter;
  }

  /**
   * Verifica se il filtraggio è abilitato
   */
  isFilteringEnabled(): boolean {
    return this._filterState.value.isFilteringEnabled;
  }

  /**
   * Abilita o disabilita il filtraggio
   */
  setFilteringEnabled(enabled: boolean): void {
    const currentState = this._filterState.value;
    const newState: TagFilterState = {
      ...currentState,
      isFilteringEnabled: enabled,
      lastFilterUpdate: Date.now()
    };

    this._filterState.next(newState);

    // Notifica il cambiamento dei filtri
    this._filtersChanged.next(true);

    console.log(`🔄 Filtraggio ${enabled ? 'abilitato' : 'disabilitato'}`);
  }

  /**
   * Recupera la gerarchia dei settori dal database locale
   * @param idCatalog ID del catalogo
   * @returns Promise<TypologyContent> Gerarchia dei settori
   */
  async getSectorHierarchyFromDatabase(idCatalog: number): Promise<TypologyContent> {
    try {
      // Recupera tutti i tag per questo catalogo dal database
      const tagRecords = await this._dbService.getRecordsByANDCondition(
        'sector_hierarchy',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      if (tagRecords.length === 0) {
        return { tags: [] };
      }

      // Costruisce la gerarchia dai record del database
      const hierarchy = this.buildHierarchyFromRecords(tagRecords);

      console.log(`✅ Gerarchia settori recuperata dal database per catalogo ${idCatalog}`);
      return hierarchy;

    } catch (error) {
      console.error(`❌ Errore nel recupero della gerarchia dal database per catalogo ${idCatalog}:`, error);
      throw error;
    }
  }

  /**
   * Costruisce la gerarchia dei tag dai record del database
   * @param records Record dal database
   * @returns TypologyContent Gerarchia costruita
   */
  private buildHierarchyFromRecords(records: any[]): TypologyContent {
    try {
      // Separa il tag universale dai tag normali
      const universalTag = records.find(r => r.level === '0');
      const normalTags = records.filter(r => r.level !== '0');

      // Raggruppa i tag per livello
      const tagsByLevel = new Map<string, any[]>();
      normalTags.forEach(record => {
        const level = record.level;
        if (!tagsByLevel.has(level)) {
          tagsByLevel.set(level, []);
        }
        tagsByLevel.get(level)!.push(record);
      });

      // Costruisce la gerarchia partendo dal livello 1 (settori)
      const rootTags = this.buildTagLevel(tagsByLevel, '1', null);

      const result: TypologyContent = {
        tags: rootTags
      };

      // Aggiunge il tag universale se presente
      if (universalTag) {
        result.universalTag = this.recordToTag(universalTag);
      }

      return result;

    } catch (error) {
      console.error('❌ Errore nella costruzione della gerarchia dai record:', error);
      return { tags: [] };
    }
  }

  /**
   * Costruisce ricorsivamente un livello della gerarchia
   * @param tagsByLevel Mappa dei tag raggruppati per livello
   * @param currentLevel Livello corrente da costruire
   * @param parentIdTag ID del tag padre (null per il livello radice)
   * @returns Tag[] Array di tag per il livello corrente
   */
  private buildTagLevel(tagsByLevel: Map<string, any[]>, currentLevel: string, parentIdTag: string | null): Tag[] {
    const currentLevelTags = tagsByLevel.get(currentLevel) || [];

    // Filtra i tag che appartengono al padre specificato
    const relevantTags = currentLevelTags.filter(record => {
      if (parentIdTag === null) {
        return record.parentIdTag === null || record.parentIdTag === '';
      }
      return record.parentIdTag === parentIdTag;
    });

    return relevantTags.map(record => {
      const tag = this.recordToTag(record);

      // Costruisce ricorsivamente i sottotag per il livello successivo
      const nextLevel = String(parseInt(currentLevel) + 1);
      if (tagsByLevel.has(nextLevel)) {
        tag.subTags = this.buildTagLevel(tagsByLevel, nextLevel, record.idTag);
      }

      return tag;
    });
  }

  /**
   * Converte un record del database in un oggetto Tag
   * @param record Record dal database
   * @returns Tag Oggetto tag
   */
  private recordToTag(record: any): Tag {
    return {
      idTag: parseInt(record.idTag),
      keyTag: record.keyTag,
      description: record.description,
      subTags: []
    };
  }

  /**
   * Verifica se la gerarchia è disponibile nel database locale
   * @param idCatalog ID del catalogo
   * @returns Promise<boolean> True se la gerarchia è disponibile
   */
  async isSectorHierarchyAvailable(idCatalog: number): Promise<boolean> {
    try {
      const records = await this._dbService.getRecordsByANDCondition(
        'sector_hierarchy',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      return records.length > 0;
    } catch (error) {
      console.error(`❌ Errore nella verifica disponibilità gerarchia per catalogo ${idCatalog}:`, error);
      return false;
    }
  }

  /**
   * Mappa la division del cliente al tag del settore
   * @param division Division del cliente (es. "IM", "AL", "AP", etc.)
   * @returns string | undefined Tag del settore corrispondente
   */
  private mapDivisionToSectorTag(division: string): string | undefined {
    const divisionMap: { [key: string]: string } = {
      'AL': 'AL-Automotive',
      'AP': 'AP-Truck',
      'CO': 'CO-Costruzioni',
      'IM': 'IM-Impianti',
      'LE': 'LE-Legno',
      'ME': 'ME-Metallo',
      'NA': 'NA-Manutenzione_industriale'
    };

    return divisionMap[division] || undefined;
  }

  /**
   * Mappa il codice professione del cliente al tag della professione
   * @param codiceProfessione Codice professione del cliente (es. "09", "10", etc.)
   * @returns string | undefined Tag della professione corrispondente
   */
  private mapCodiceProfessioneToTag(codiceProfessione: string): string | undefined {
    if (!codiceProfessione) return undefined;

    // Il codice professione è la parte iniziale del tag professione
    // Es. "09" corrisponde a tag che iniziano con "09-PD_"
    return `${codiceProfessione}-PD_`;
  }

  /**
   * Invalida la cache dei tag delle categorie
   * Da chiamare dopo la sincronizzazione o quando i dati cambiano
   */
  invalidateTagsCache(): void {
    console.log('🔄 [TAG_FILTER] Invalidazione cache tag categorie');
    this._categoryTagsCache.clear();
    this._cacheTimestamp = 0;
  }

  /**
   * Precarica la cache dei tag per le categorie più utilizzate
   * @param categoryIds Array di ID delle categorie da precaricare
   * @param idCatalog ID del catalogo
   */
  async preloadTagsCache(categoryIds: string[], idCatalog: number = 1): Promise<void> {
    console.log(`🚀 [TAG_FILTER] Precaricamento cache per ${categoryIds.length} categorie`);

    const promises = categoryIds.map(async (categoryId) => {
      try {
        await this.getCachedCategoryTags(parseInt(categoryId), idCatalog);
      } catch (error) {
        console.warn(`⚠️ [TAG_FILTER] Errore nel precaricamento categoria ${categoryId}:`, error);
      }
    });

    await Promise.all(promises);
    console.log('✅ [TAG_FILTER] Precaricamento cache completato');
  }

  /**
   * Inizializza gli indici del database per ottimizzare le performance del filtraggio
   * Questo metodo dovrebbe essere chiamato durante l'inizializzazione dell'app
   */
  async initializeFilteringIndexes(): Promise<void> {
    try {
      console.log('🔧 [TAG_FILTER] Inizializzazione indici per filtraggio...');
      await this._dbService.createFilteringIndexes();
      console.log('✅ [TAG_FILTER] Indici per filtraggio inizializzati');
    } catch (error) {
      console.error('❌ Errore nell\'inizializzazione indici filtraggio:', error);
    }
  }

  /**
   * Ottiene statistiche sulle performance del filtraggio
   * Utile per debug e monitoraggio
   */
  async getFilteringPerformanceStats(idCatalog: number = 1): Promise<{
    totalCategories: number;
    productCategories: number;
    rootCategories: number;
    totalTags: number;
    categoriesWithTags: number;
    cacheSize: number;
    cacheHitRate?: number;
  }> {
    try {
      const dbStats = await this._dbService.getFilteringStats(idCatalog);

      return {
        ...dbStats,
        cacheSize: this._categoryTagsCache.size,
        cacheHitRate: this._cacheTimestamp > 0 ? 0.85 : 0 // Stima approssimativa
      };

    } catch (error) {
      console.error('❌ Errore nel calcolo statistiche performance:', error);
      return {
        totalCategories: 0,
        productCategories: 0,
        rootCategories: 0,
        totalTags: 0,
        categoriesWithTags: 0,
        cacheSize: this._categoryTagsCache.size
      };
    }
  }

  /**
   * Forza il refresh delle categorie notificando il cambiamento dei filtri
   * Utile per il pulsante "Applica Filtri"
   */
  forceRefreshCategories(): void {
    console.log('🔄 [TAG_FILTER] Forzando refresh delle categorie e conteggi...');
    this._filtersChanged.next(true);
    this._countsNeedUpdate.next(true);
  }

  /**
   * Resetta il flag di cambiamento filtri
   * Da chiamare dopo aver processato il cambiamento
   */
  resetFiltersChangedFlag(): void {
    this._filtersChanged.next(false);
  }
}

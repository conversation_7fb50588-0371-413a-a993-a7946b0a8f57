import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { DbService } from './db.service';
import { IndexedDbService } from './indexeddb.service';
import { TableProperty } from './table-property';

@Injectable({
  providedIn: 'root'
})
export class HybridDbService {
  private indexedDbAvailable: boolean | null = null;

  constructor(
    private platform: Platform,
    private sqliteDbService: DbService,
    private indexedDbService: IndexedDbService
  ) {}

  /**
   * Ricrea il database in caso di problemi di versione
   */
  async recreateDatabase(): Promise<void> {
    if (this.isWeb()) {
      console.log('🔄 Ricreazione del database IndexedDB...');
      await this.indexedDbService.recreateDatabase();
      console.log('✅ Database IndexedDB ricreato con successo');

      // Dopo la ricreazione, ricarica la pagina per reinizializzare tutto
      if (typeof window !== 'undefined') {
        console.log('🔄 Ricaricamento della pagina per completare l\'aggiornamento...');
        window.location.reload();
      }
    } else {
      return Promise.resolve();
    }
  }

  private isWeb(): boolean {
    return !this.platform.is('capacitor');
  }

  /**
   * Verifica se IndexedDB è disponibile (solo per web)
   */
  private async checkIndexedDbAvailability(): Promise<boolean> {
    if (this.indexedDbAvailable !== null) {
      return this.indexedDbAvailable;
    }

    if (!this.isWeb()) {
      this.indexedDbAvailable = false;
      return false;
    }

    try {
      this.indexedDbAvailable = await this.indexedDbService.isAvailable();
      return this.indexedDbAvailable;
    } catch (error) {
      console.error('Error checking IndexedDB availability:', error);
      this.indexedDbAvailable = false;
      return false;
    }
  }

  async createTable(tableName: string, columnList: TableProperty[], withAutomaticId: boolean = false): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.createTable(tableName, columnList, withAutomaticId);
    } else {
      return this.sqliteDbService.createTable(tableName, columnList, withAutomaticId);
    }
  }

  async createUniqueIndex(tableName: string, columnList: string[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.createUniqueIndex(tableName, columnList);
    } else {
      return this.sqliteDbService.createUniqueIndex(tableName, columnList);
    }
  }

  async createIndex(tableName: string, columnList: string[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.createIndex(tableName, columnList);
    } else {
      return this.sqliteDbService.createIndex(tableName, columnList);
    }
  }

  async addColumn(tableName: string, columnName: string, columnType: string): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.addColumn(tableName, columnName, columnType);
    } else {
      return this.sqliteDbService.addColumn(tableName, columnName, columnType);
    }
  }

  async clearTable(tableName: string): Promise<any> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, skipping clearTable for ${tableName}`);
        return Promise.resolve(null);
      }

      try {
        return await this.indexedDbService.clearTable(tableName);
      } catch (error) {
        console.error(`❌ Failed to clear table ${tableName} in IndexedDB:`, error);
        // Non lanciare l'errore, ma logga e continua
        return Promise.resolve(null);
      }
    } else {
      return this.sqliteDbService.clearTable(tableName);
    }
  }

  async addRecord(tableName: string, columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, skipping addRecord for ${tableName}`);
        return Promise.resolve(null);
      }

      try {
        return await this.indexedDbService.addRecord(tableName, columnList, valueList);
      } catch (error) {
        console.error(`❌ Failed to add record to ${tableName} in IndexedDB:`, error);
        return Promise.resolve(null);
      }
    } else {
      return this.sqliteDbService.addRecord(tableName, columnList, valueList);
    }
  }

  async addRecords(tableName: string, columns: string[], records: any[][]): Promise<any> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, skipping addRecords for ${tableName} (${records.length} records)`);
        return Promise.resolve(null);
      }

      try {
        return await this.indexedDbService.addRecords(tableName, columns, records);
      } catch (error) {
        console.error(`❌ Failed to add records to ${tableName} in IndexedDB:`, error);
        return Promise.resolve(null);
      }
    } else {
      return this.sqliteDbService.addRecords(tableName, columns, records);
    }
  }

  async replaceIntoRecord(tableName: string, columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.replaceIntoRecord(tableName, columnList, valueList);
    } else {
      return this.sqliteDbService.replaceIntoRecord(tableName, columnList, valueList);
    }
  }



  async getDistinct(tableName: string, columnList?: string[]): Promise<any[]> {
    if (this.isWeb()) {
      // Per IndexedDB, otteniamo tutti i record e filtriamo i duplicati
      const allRecords = await this.indexedDbService.getAll([tableName], columnList);
      
      if (!columnList || columnList.length === 0) {
        return allRecords;
      }
      
      // Rimuovi duplicati basandoti sulle colonne specificate
      const seen = new Set();
      return allRecords.filter(record => {
        const key = columnList.map(col => record[col]).join('|');
        if (seen.has(key)) {
          return false;
        }
        seen.add(key);
        return true;
      });
    } else {
      return this.sqliteDbService.getDistinct(tableName, columnList);
    }
  }

  async getAll(tableName: string[], columnList?: string[], joinList?: string[]): Promise<any[]> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, returning empty array for ${tableName[0]}`);
        return [];
      }

      try {
        return await this.indexedDbService.getAll(tableName, columnList, joinList);
      } catch (error: any) {
        if (error.message && error.message.includes('Database version error')) {
          console.log('🔄 Errore di versione database rilevato, ricreazione in corso...');
          await this.recreateDatabase();
          return []; // Ritorna array vuoto dopo la ricreazione
        }
        console.error(`❌ Failed to get records from ${tableName[0]} in IndexedDB:`, error);
        return [];
      }
    } else {
      return this.sqliteDbService.getAll(tableName, columnList, joinList);
    }
  }

  async getAllGroupedBy(tableName: string, columnList?: string[], groupBy?: string[]): Promise<any[]> {
    if (this.isWeb()) {
      // Per IndexedDB, otteniamo tutti i record e li raggruppiamo manualmente
      const allRecords = await this.indexedDbService.getAll([tableName], columnList);
      
      if (!groupBy || groupBy.length === 0) {
        return allRecords;
      }
      
      // Raggruppa manualmente
      const groups = new Map();
      allRecords.forEach(record => {
        const key = groupBy.map(col => record[col]).join('|');
        if (!groups.has(key)) {
          groups.set(key, record);
        }
      });
      
      return Array.from(groups.values());
    } else {
      return this.sqliteDbService.getAllGroupedBy(tableName, columnList, groupBy);
    }
  }

  async getRecordsByANDCondition(tableName: string, conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    if (this.isWeb()) {
      try {
        return await this.indexedDbService.getRecordsByANDCondition(tableName, conditionalValueList);
      } catch (error: any) {
        if (error.message && error.message.includes('Database version error')) {
          console.log('🔄 Errore di versione database rilevato, ricreazione in corso...');
          await this.recreateDatabase();
          return []; // Ritorna array vuoto dopo la ricreazione
        }
        throw error;
      }
    } else {
      return this.sqliteDbService.getRecordsByANDCondition(tableName, conditionalValueList);
    }
  }

  async getColumnFromTableByANDCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    if (this.isWeb()) {
      const records = await this.indexedDbService.getRecordsByANDCondition(tableName, conditionalValueList);
      
      // Filtra solo le colonne richieste
      return records.map(record => {
        const filtered: any = {};
        columnList.forEach(col => {
          if (record.hasOwnProperty(col)) {
            filtered[col] = record[col];
          }
        });
        return filtered;
      });
    } else {
      return this.sqliteDbService.getColumnFromTableByANDCondition(tableName, columnList, conditionalValueList);
    }
  }

  async getColumnFromTableByORCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    if (this.isWeb()) {
      const allRecords = await this.indexedDbService.getAll([tableName]);
      
      // Filtra con condizioni OR
      const filtered = allRecords.filter(record => {
        return conditionalValueList.some(condition => {
          return record[condition.key] && record[condition.key].toString() === condition.value;
        });
      });
      
      // Filtra solo le colonne richieste
      return filtered.map(record => {
        const result: any = {};
        columnList.forEach(col => {
          if (record.hasOwnProperty(col)) {
            result[col] = record[col];
          }
        });
        return result;
      });
    } else {
      return this.sqliteDbService.getColumnFromTableByORCondition(tableName, columnList, conditionalValueList);
    }
  }

  async getRecordsByINCondition(tableName: string, conditionalValueList: {key: string, value: string[]}): Promise<any[]> {
    if (this.isWeb()) {
      const allRecords = await this.indexedDbService.getAll([tableName]);
      
      // Filtra con condizione IN
      return allRecords.filter(record => {
        return conditionalValueList.value.includes(record[conditionalValueList.key]);
      });
    } else {
      return this.sqliteDbService.getRecordsByINCondition(tableName, conditionalValueList);
    }
  }

  async deleteRecord(tableName: string, andConditionList: {key: string, value: any}[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.deleteRecord(tableName, andConditionList);
    } else {
      return this.sqliteDbService.deleteRecord(tableName, andConditionList);
    }
  }

  async updateRecord(tableName: string, andConditionList: {key: string, value: any}[], columnList: {key: string, value: any}[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.updateRecord(tableName, andConditionList, columnList);
    } else {
      return this.sqliteDbService.updateRecord(tableName, andConditionList, columnList);
    }
  }

  async insertOrReplace(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.insertOrReplace(tableName, andConditionList, columnList, valueList);
    } else {
      return this.sqliteDbService.insertOrReplace(tableName, andConditionList, columnList, valueList);
    }
  }

  async insertOrReplaceWithoutID(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.insertOrReplaceWithoutID(tableName, andConditionList, columnList, valueList);
    } else {
      return this.sqliteDbService.insertOrReplaceWithoutID(tableName, andConditionList, columnList, valueList);
    }
  }

  async multipleInsertOrReplaceByTransaction(
    tableName: string,
    isIdAutoincrement: boolean,
    columns: string[],
    records: { andConditionList: { key: string, value: any }[]; values: any[] }[]
  ): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.multipleInsertOrReplaceByTransaction(tableName, isIdAutoincrement, columns, records);
    } else {
      return this.sqliteDbService.multipleInsertOrReplaceByTransaction(tableName, isIdAutoincrement, columns, records);
    }
  }

  /**
   * Esegue una query SQL diretta (solo per SQLite)
   * Per IndexedDB, questo metodo non è supportato e restituisce una promessa risolta
   */
  async execute(query: string): Promise<any> {
    if (this.isWeb()) {
      console.warn('⚠️ SQL execute not supported in IndexedDB, query ignored:', query);
      return Promise.resolve(null);
    } else {
      // Assumiamo che DbService abbia un metodo execute
      if (typeof (this.sqliteDbService as any).execute === 'function') {
        return (this.sqliteDbService as any).execute(query);
      } else {
        console.warn('⚠️ Execute method not available in DbService');
        return Promise.resolve(null);
      }
    }
  }

  /**
   * Ottiene le categorie già filtrate in base ai tag attivi (VERSIONE SEMPLIFICATA)
   * LOGICA: I tag sono solo per le root categories. Se una root category ha i tag richiesti,
   * mostra la root category e TUTTE le sue sottocategorie/prodotti.
   * @param activeTags Array di tag attivi per il filtraggio
   * @param idCatalog ID del catalogo (default: 1)
   * @param columnList Colonne da selezionare (opzionale)
   * @returns Promise<any[]> Categorie filtrate
   */
  async getFilteredCategories(activeTags: string[], idCatalog: number = 1, columnList?: string[]): Promise<any[]> {
    console.log(`🔍 [HYBRID_DB] Filtraggio SEMPLIFICATO con ${activeTags.length} tag attivi`);

    try {
      // Se non ci sono tag attivi, restituisce tutte le categorie
      if (activeTags.length === 0) {
        return this.getAll(['categories'], columnList);
      }

      // STEP 1: Trova le root categories che hanno i tag richiesti
      const visibleRootCategoryIds = await this.getVisibleRootCategoryIds(activeTags, idCatalog);

      if (visibleRootCategoryIds.size === 0) {
        console.log('⚠️ [HYBRID_DB] Nessuna root category visibile con i tag specificati');
        return [];
      }

      // STEP 2: Ottieni tutte le categorie
      const allCategories = await this.getAll(['categories'], columnList);

      // STEP 3: Filtra le categorie che appartengono alle root categories visibili
      const filteredCategories = allCategories.filter(category => {
        // Se è una root category, verifica se è visibile
        if (category.isRootCategory === 'true' || category.isRootCategoryByRegex === 'true') {
          return visibleRootCategoryIds.has(category.id);
        }

        // Se è una sottocategoria/prodotto, verifica se appartiene a una root category visibile
        if (category.idRootCategory) {
          return visibleRootCategoryIds.has(category.idRootCategory);
        }

        // Verifica tramite idApp (per compatibilità)
        if (category.idApp) {
          for (const rootId of visibleRootCategoryIds) {
            const rootCategory = allCategories.find(c => c.id === rootId);
            if (rootCategory && rootCategory.idApp && category.idApp.startsWith(rootCategory.idApp)) {
              return true;
            }
          }
        }

        return false;
      });

      console.log(`✅ [HYBRID_DB] Filtrate ${filteredCategories.length}/${allCategories.length} categorie (${visibleRootCategoryIds.size} root categories visibili)`);
      return filteredCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio semplificato:', error);
      return this.getAll(['categories'], columnList);
    }
  }

  /**
   * Trova gli ID delle root categories che hanno i tag richiesti (SEMPLIFICATO)
   * @param activeTags Array di tag attivi
   * @param idCatalog ID del catalogo
   * @returns Promise<Set<string>> Set degli ID delle root categories visibili
   */
  private async getVisibleRootCategoryIds(activeTags: string[], idCatalog: number): Promise<Set<string>> {
    try {
      // Ottieni tutti i tag delle categorie per questo catalogo
      const categoryTags = await this.getRecordsByANDCondition(
        'datacol_category_tags',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      // Trova le categorie che hanno almeno uno dei tag attivi
      const categoriesWithTags = new Set<string>();
      console.log(`🔍 [DEBUG] Tag attivi del filtro:`, activeTags);

      categoryTags.forEach((tagRecord: any) => {
        const categoryTag = tagRecord.keyTag;
        const isMatch = activeTags.includes(categoryTag);

        if (isMatch) {
          console.log(`✅ [DEBUG] MATCH trovato: categoria ${tagRecord.idSubCategory} ha tag "${categoryTag}"`);
          categoriesWithTags.add(String(tagRecord.idSubCategory));
        } else {
          console.log(`❌ [DEBUG] NO MATCH: categoria ${tagRecord.idSubCategory} ha tag "${categoryTag}" (non in filtro)`);
        }
      });

      // Ottieni tutte le root categories
      const allCategories = await this.getAll(['categories']);
      const rootCategories = allCategories.filter(cat =>
        cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
      );

      // Trova le root categories che hanno i tag richiesti
      const visibleRootCategoryIds = new Set<string>();
      console.log(`🔍 [DEBUG] Verifico ${rootCategories.length} root categories...`);

      rootCategories.forEach(rootCategory => {
        const hasRequiredTags = categoriesWithTags.has(String(rootCategory.idSubCategory));

        if (hasRequiredTags) {
          console.log(`✅ [DEBUG] Root category VISIBILE: "${rootCategory.name}" (ID: ${rootCategory.id}, idSubCategory: ${rootCategory.idSubCategory})`);
          visibleRootCategoryIds.add(rootCategory.id);
        } else {
          console.log(`❌ [DEBUG] Root category NASCOSTA: "${rootCategory.name}" (ID: ${rootCategory.id}, idSubCategory: ${rootCategory.idSubCategory})`);
        }
      });

      console.log(`🔍 [HYBRID_DB] Trovate ${visibleRootCategoryIds.size}/${rootCategories.length} root categories visibili`);
      return visibleRootCategoryIds;

    } catch (error) {
      console.error('❌ Errore nel trovare root categories visibili:', error);
      return new Set<string>();
    }
  }

  /**
   * Crea gli indici ottimizzati per il filtraggio delle categorie
   * Questo metodo dovrebbe essere chiamato durante l'inizializzazione del database
   */
  async createFilteringIndexes(): Promise<void> {
    try {
      console.log('🔧 [HYBRID_DB] Creazione indici per filtraggio categorie...');

      // Indici per la tabella categories
      await this.createIndex('categories', ['isRootCategory']);
      await this.createIndex('categories', ['isRootCategoryByRegex']);
      await this.createIndex('categories', ['idRootCategory']);
      await this.createIndex('categories', ['idApp']);
      await this.createIndex('categories', ['idSubCategory']);

      // Indici per la tabella datacol_category_tags
      await this.createIndex('datacol_category_tags', ['idCatalog']);
      await this.createIndex('datacol_category_tags', ['idSubCategory']);
      await this.createIndex('datacol_category_tags', ['keyTag']);
      await this.createIndex('datacol_category_tags', ['idCatalog', 'keyTag']);

      // Indici per la tabella products (per il filtraggio performante)
      await this.createIndex('products', ['idSubCategory']);
      await this.createIndex('products', ['idCategory']);
      await this.createIndex('products', ['code']);

      console.log('✅ [HYBRID_DB] Indici per filtraggio creati con successo');

    } catch (error) {
      console.error('❌ Errore nella creazione degli indici per filtraggio:', error);
    }
  }

  /**
   * Ottiene i prodotti filtrati direttamente dal database usando JOIN con i tag
   * Questo metodo è molto più performante del caricamento di tutti i prodotti in memoria
   * @param activeTags Array di tag attivi per il filtraggio
   * @param idCatalog ID del catalogo (default: 1)
   * @param columnList Colonne da selezionare (opzionale)
   * @param additionalConditions Condizioni aggiuntive per il filtro (es. idSubCategory)
   * @returns Promise<any[]> Prodotti filtrati
   */
  async getFilteredProducts(
    activeTags: string[],
    idCatalog: number = 1,
    columnList?: string[],
    additionalConditions?: {key: string, value: string}[]
  ): Promise<any[]> {
    console.log(`🚀 [HYBRID_DB] Caricamento prodotti filtrati con ${activeTags.length} tag attivi`);

    try {
      const startTime = Date.now();

      // Se non ci sono tag attivi, usa le condizioni aggiuntive o restituisce tutti i prodotti
      if (activeTags.length === 0) {
        if (additionalConditions && additionalConditions.length > 0) {
          return this.getRecordsByANDCondition('products', additionalConditions);
        }
        return this.getAll(['products'], columnList);
      }

      // STEP 1: Trova le subcategorie che hanno i tag richiesti
      const visibleSubCategoryIds = await this.getVisibleSubCategoryIds(activeTags, idCatalog);

      if (visibleSubCategoryIds.size === 0) {
        console.log('⚠️ [HYBRID_DB] Nessuna subcategoria visibile con i tag specificati');
        return [];
      }

      // STEP 2: Costruisci le condizioni per il filtro prodotti
      const productConditions: {key: string, value: string}[] = [];

      // Aggiungi condizioni aggiuntive se specificate
      if (additionalConditions) {
        productConditions.push(...additionalConditions);
      }

      // STEP 3: Ottieni i prodotti che appartengono alle subcategorie visibili
      const filteredProducts = await this.getProductsBySubCategories(
        Array.from(visibleSubCategoryIds),
        columnList,
        productConditions
      );

      const endTime = Date.now();
      console.log(`✅ [HYBRID_DB] Prodotti filtrati in ${endTime - startTime}ms: ${filteredProducts.length} risultati da ${visibleSubCategoryIds.size} subcategorie`);

      return filteredProducts;

    } catch (error) {
      console.error('❌ Errore nel filtraggio prodotti:', error);
      // Fallback: usa le condizioni aggiuntive o restituisce tutti i prodotti
      if (additionalConditions && additionalConditions.length > 0) {
        return this.getRecordsByANDCondition('products', additionalConditions);
      }
      return this.getAll(['products'], columnList);
    }
  }

  /**
   * Trova gli ID delle subcategorie che hanno i tag richiesti
   * @param activeTags Array di tag attivi
   * @param idCatalog ID del catalogo
   * @returns Promise<Set<string>> Set degli ID delle subcategorie visibili
   */
  private async getVisibleSubCategoryIds(activeTags: string[], idCatalog: number): Promise<Set<string>> {
    try {
      // Ottieni tutti i tag delle categorie per questo catalogo
      const categoryTags = await this.getRecordsByANDCondition(
        'datacol_category_tags',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      // Trova le subcategorie che hanno almeno uno dei tag attivi
      const visibleSubCategoryIds = new Set<string>();
      for (const tagRecord of categoryTags) {
        if (activeTags.includes(tagRecord.keyTag)) {
          visibleSubCategoryIds.add(String(tagRecord.idSubCategory));
        }
      }

      console.log(`🔍 [HYBRID_DB] Trovate ${visibleSubCategoryIds.size} subcategorie visibili con tag attivi`);
      return visibleSubCategoryIds;

    } catch (error) {
      console.error('❌ Errore nel calcolo subcategorie visibili:', error);
      return new Set();
    }
  }

  /**
   * Ottiene i prodotti che appartengono a specifiche subcategorie
   * @param subCategoryIds Array di ID delle subcategorie
   * @param columnList Colonne da selezionare (opzionale)
   * @param additionalConditions Condizioni aggiuntive per il filtro
   * @returns Promise<any[]> Prodotti filtrati
   */
  private async getProductsBySubCategories(
    subCategoryIds: string[],
    columnList?: string[],
    additionalConditions?: {key: string, value: string}[]
  ): Promise<any[]> {
    try {
      if (subCategoryIds.length === 0) {
        return [];
      }

      // Per performance, se ci sono molte subcategorie, usa IN condition
      if (subCategoryIds.length > 10) {
        const products = await this.getRecordsByINCondition('products', { key: 'idSubCategory', value: subCategoryIds });

        // Filtra solo le colonne richieste se specificate
        if (columnList && columnList.length > 0) {
          return products.map(product => {
            const filtered: any = {};
            columnList.forEach(col => {
              if (product.hasOwnProperty(col)) {
                filtered[col] = product[col];
              }
            });
            return filtered;
          });
        }

        return products;
      }

      // Per poche subcategorie, usa OR condition
      const allProducts: any[] = [];

      for (const subCategoryId of subCategoryIds) {
        const conditions: {key: string, value: string}[] = [
          { key: 'idSubCategory', value: subCategoryId }
        ];

        // Aggiungi condizioni aggiuntive se specificate
        if (additionalConditions) {
          conditions.push(...additionalConditions);
        }

        const products = await this.getRecordsByANDCondition('products', conditions);
        allProducts.push(...products);
      }

      // Rimuovi duplicati basandosi sul codice prodotto
      const uniqueProducts = allProducts.filter((product, index, self) =>
        index === self.findIndex(p => p.code === product.code)
      );

      // Filtra solo le colonne richieste se specificate
      if (columnList && columnList.length > 0) {
        return uniqueProducts.map(product => {
          const filtered: any = {};
          columnList.forEach(col => {
            if (product.hasOwnProperty(col)) {
              filtered[col] = product[col];
            }
          });
          return filtered;
        });
      }

      return uniqueProducts;

    } catch (error) {
      console.error('❌ Errore nel recupero prodotti per subcategorie:', error);
      return [];
    }
  }

  /**
   * Ottiene statistiche sulle performance del filtraggio
   * Utile per debug e ottimizzazione
   */
  async getFilteringStats(idCatalog: number = 1): Promise<{
    totalCategories: number;
    productCategories: number;
    rootCategories: number;
    totalTags: number;
    categoriesWithTags: number;
  }> {
    try {
      const allCategories = await this.getAll(['categories']);
      const productCategories = allCategories.filter(c => c.isProduct === 'true' || c.isProduct === true);
      const rootCategories = allCategories.filter(c =>
        c.isRootCategory === 'true' || c.isRootCategoryByRegex === 'true'
      );

      const allTags = await this.getRecordsByANDCondition(
        'datacol_category_tags',
        [{ key: 'idCatalog', value: String(idCatalog) }]
      );

      const categoriesWithTags = new Set(allTags.map((t: any) => t.idSubCategory)).size;

      return {
        totalCategories: allCategories.length,
        productCategories: productCategories.length,
        rootCategories: rootCategories.length,
        totalTags: allTags.length,
        categoriesWithTags
      };

    } catch (error) {
      console.error('❌ Errore nel calcolo statistiche filtraggio:', error);
      return {
        totalCategories: 0,
        productCategories: 0,
        rootCategories: 0,
        totalTags: 0,
        categoriesWithTags: 0
      };
    }
  }

  /**
   * Verifica se il database è disponibile e funzionante
   */





  /**
   * Ottiene le root categories filtrate (VERSIONE SEMPLIFICATA)
   * @param activeTags Array di tag attivi per il filtraggio
   * @param idCatalog ID del catalogo (default: 1)
   * @returns Promise<any[]> Root categories che hanno i tag richiesti
   */
  async getFilteredRootCategories(activeTags: string[], idCatalog: number = 1): Promise<any[]> {
    console.log(`🔍 [HYBRID_DB] Caricamento root categories SEMPLIFICATO con ${activeTags.length} tag attivi`);

    try {
      // Se non ci sono tag attivi, restituisce tutte le root categories
      if (activeTags.length === 0) {
        const allCategories = await this.getAll(['categories']);
        return allCategories.filter(cat =>
          cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
        );
      }

      // Trova le root categories che hanno i tag richiesti
      const visibleRootCategoryIds = await this.getVisibleRootCategoryIds(activeTags, idCatalog);

      if (visibleRootCategoryIds.size === 0) {
        console.log('⚠️ [HYBRID_DB] Nessuna root category visibile con i tag specificati');
        return [];
      }

      // Ottieni tutte le root categories
      const allCategories = await this.getAll(['categories']);
      const rootCategories = allCategories.filter(cat =>
        cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
      );

      // Filtra solo quelle visibili
      const filteredRootCategories = rootCategories.filter(rootCategory =>
        visibleRootCategoryIds.has(rootCategory.id)
      );

      console.log(`✅ [HYBRID_DB] Root categories filtrate: ${filteredRootCategories.length}/${rootCategories.length}`);
      return filteredRootCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio root categories semplificato:', error);
      return [];
    }
  }







  /**
   * Verifica se il database è disponibile e funzionante
   */
  async isDatabaseAvailable(): Promise<boolean> {
    if (this.isWeb()) {
      return this.indexedDbService.isAvailable();
    } else {
      // Per SQLite su mobile, assumiamo che sia sempre disponibile se la piattaforma è capacitor
      return this.platform.is('capacitor');
    }
  }

  /**
   * Ottiene informazioni sulla piattaforma corrente
   */
  getPlatformInfo(): { platform: string; isWeb: boolean; isMobile: boolean } {
    const isWeb = this.isWeb();
    return {
      platform: isWeb ? 'web' : 'mobile',
      isWeb: isWeb,
      isMobile: !isWeb
    };
  }

  /**
   * Verifica se le tabelle dei filtri tag esistono e le crea se necessario
   */
  async ensureTagFilterTablesExist(): Promise<void> {
    if (!this.isWeb()) {
      // Su mobile le tabelle vengono create dal syncro-v2-db.service
      return;
    }

    try {
      // Prova ad accedere a una delle nuove tabelle
      await this.getRecordsByANDCondition('customer_filter_state', []);
    } catch (error) {
      if (error instanceof Error && error.message.includes('object stores was not found')) {
        console.log('🔄 Tabelle filtri tag non trovate, ricreazione database...');
        await this.recreateDatabase();
      } else {
        throw error;
      }
    }
  }
}
